//
//  ContentView.swift
//  cadence
//
//  Created by <PERSON> on 12/5/24.
//

import SwiftUI
import AVFoundation
import MediaPlayer
import SwiftData

class MetronomePlayer: ObservableObject {

    private var audioPlayer: AVAudioPlayer?
    private var timer: Timer?
    @Published var isPlaying = false
    @Published var hit = false
    private var currentBPM: Double = 180
    
    init() {
        let url = Bundle.main.url(forResource: "metronome", withExtension: "mp3")
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url!)
            audioPlayer?.numberOfLoops = -1
            audioPlayer?.prepareToPlay()
            audioPlayer?.volume = 1.0  // 设置最大音量
        } catch {
            print(error.localizedDescription)
        }
    }
    
    func startMetronome(bpm: Double) {
        isPlaying = true
        do {
            if #available(iOS 10.0, *) {
                try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            } else {
                // Fallback on earlier versions
            }
            try AVAudioSession.sharedInstance().setActive(true)

            guard let player = audioPlayer else {
                return
            }
            let interval = 60.0 / bpm
            timer?.invalidate()
            timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
                self?.hit.toggle()
            }
            // 设置播放速率
            player.enableRate = true
            player.rate = Float(bpm / 180.0)
            player.play()
        } catch {
            print("ops")
        }
        audioPlayer?.play()
    }
    
    func stopMetronome() {
        isPlaying = false
        audioPlayer?.stop()
        timer?.invalidate()
        timer = nil
        hit = false
    }

    func updateBPM(_ bpm: Double) {
        currentBPM = bpm
        if isPlaying {
            audioPlayer?.rate = Float(bpm / 180.0)
        }
    }
    
    func setVolume(_ volume: Float) {
        audioPlayer?.volume = volume
    }
}

class MusicPlayer: ObservableObject {
    private var remoteCommandCenter: MPRemoteCommandCenter
    @Published var isPlayingMusic = false
    @Published var currentTrackTitle = "No track playing"
    
    init() {
        remoteCommandCenter = MPRemoteCommandCenter.shared()
        setupRemoteCommands()
    }
    
    private func setupRemoteCommands() {
        // 启用远程控制命令
        remoteCommandCenter.playCommand.addTarget { [weak self] _ in
            self?.playMusic()
            return .success
        }
        
        remoteCommandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pauseMusic()
            return .success
        }
        
        remoteCommandCenter.nextTrackCommand.addTarget { [weak self] _ in
            self?.nextTrack()
            return .success
        }
        
        remoteCommandCenter.previousTrackCommand.addTarget { [weak self] _ in
            self?.previousTrack()
            return .success
        }
    }
    
    func playMusic() {
        // 检查是否有活动的音乐播放会话
        if !AVAudioSession.sharedInstance().isOtherAudioPlaying {
            // 如果没有其他音频在播放，尝试播放 Apple Music 或播客
            MPMusicPlayerController.systemMusicPlayer.play()
        }
        
        // 更新状态
        isPlayingMusic = true
        updateNowPlayingInfo()
    }
    
    func pauseMusic() {
        // 暂停系统音乐播放器
        MPMusicPlayerController.systemMusicPlayer.pause()
        
        // 更新状态
        isPlayingMusic = false
        updateNowPlayingInfo()
    }
    
    func nextTrack() {
        MPMusicPlayerController.systemMusicPlayer.skipToNextItem()
    }
    
    func previousTrack() {
        MPMusicPlayerController.systemMusicPlayer.skipToPreviousItem()
    }
    
    private func updateNowPlayingInfo() {
        // 更新锁屏和控制中心的播放信息
        var nowPlayingInfo = [String: Any]()
        
        // 获取当前播放的音乐信息
        let musicPlayer = MPMusicPlayerController.systemMusicPlayer
        if let nowPlayingItem = musicPlayer.nowPlayingItem {
            nowPlayingInfo[MPMediaItemPropertyTitle] = nowPlayingItem.title ?? "Unknown Title"
            nowPlayingInfo[MPMediaItemPropertyArtist] = nowPlayingItem.artist ?? "Unknown Artist"
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = nowPlayingItem.albumTitle ?? "Unknown Album"
            currentTrackTitle = nowPlayingItem.title ?? "Unknown Title"
        } else {
            nowPlayingInfo[MPMediaItemPropertyTitle] = "Cadence App"
            nowPlayingInfo[MPMediaItemPropertyArtist] = "Running Playlist"
            currentTrackTitle = "No track playing"
        }
        
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlayingMusic ? 1.0 : 0.0
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = musicPlayer.currentPlaybackTime
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var sliderValue: Double = 180
    @StateObject private var player = MetronomePlayer()
    @StateObject private var musicPlayer = MusicPlayer()
    @StateObject private var locationManager = LocationManager()
    @State private var isPressed = false
    @State private var showMenu = false
    @State private var showRunSummary = false
    private let feedback = UIImpactFeedbackGenerator(style: .medium)
    
    var body: some View {
        VStack {
            // 媒体播放器区域 - 顶部
            MediaPlayerView()
                .padding(.horizontal)
                .padding(.top, 10)
            
            Spacer()
            
            // 位置权限提示
            if locationManager.needsLocationPermission {
                VStack {
                    Image(systemName: "location.slash")
                        .font(.largeTitle)
                        .foregroundColor(.orange)
                    Text("需要位置权限来记录跑步轨迹")
                        .font(.headline)
                        .multilineTextAlignment(.center)
                    Text("请在设置中允许位置访问")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemYellow).opacity(0.1))
                .cornerRadius(10)
                .padding(.horizontal)
            }
            
            // GPS数据显示区域
            if locationManager.isRecording {
                VStack(spacing: 10) {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("距离")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(String(format: "%.2f km", locationManager.distance / 1000))
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                        }
                        
                        Spacer()
                        
                        VStack {
                            Text("时间")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(formatTime(locationManager.duration))
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing) {
                            Text("配速")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(locationManager.pace > 0 ? String(format: "%.1f'", locationManager.pace) : "--")
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                .padding(.horizontal)
                .transition(.opacity)
            }
            
            Text("BPM: \(Int(sliderValue))")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(locationManager.isRecording ? .green : .primary)
            
            // GPS状态指示器
            HStack {
                Image(systemName: "location")
                    .foregroundColor(locationManager.isRecording ? .green : .gray)
                Text(locationManager.isRecording ? "GPS追踪中" : "点击开始GPS追踪")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 5)

            Slider(
                value: $sliderValue,
                in: 160...220,
                step: 1
            ).accentColor(Color.green)
            .frame(width: 300, height: 50)
            Spacer()
            Image(systemName: player.isPlaying ? "figure.run" : "figure.walk")
                .symbolEffect(.wiggle, isActive: player.hit)
                .padding()
                .foregroundColor(locationManager.isRecording ? .green : .primary)
                .font(.system(size: 150))
                //.scaleEffect(isPressed ? 1.3 : 1.0)
                .animation(.spring(), value: isPressed)
                .simultaneousGesture(
                    DragGesture(minimumDistance: 0)
                        .onChanged { _ in
                            withAnimation {
                                isPressed = true
                                feedback.impactOccurred()
                            }
                        }
                        .onEnded { _ in
                            withAnimation {
                                isPressed = false
                                if player.isPlaying {
                                    // 停止节拍器和GPS追踪
                                    player.stopMetronome()
                                    locationManager.stopRecording()
                                    // 如果有运动数据则显示总结
                                    print("Distance: \(locationManager.distance), Locations: \(locationManager.locations.count)")
                                    
                                    // 调试：如果没有GPS数据，创建一些测试数据
                                    if locationManager.locations.isEmpty {
                                        print("No GPS data, creating test data")
                                        locationManager.addTestLocations()
                                    }
                                    
                                    if locationManager.distance > 10 || locationManager.locations.count > 5 { // 降低要求便于测试
                                        showRunSummary = true
                                    }
                                } else {
                                    // 开始节拍器和GPS追踪
                                    player.startMetronome(bpm: sliderValue)
                                    locationManager.startRecording()
                                }
                            }
                        }
                )
                .frame(height: 150)
            
            Spacer()
        }
        .onAppear {
            locationManager.setModelContext(modelContext)
        }
        .padding()
        .onChange(of: sliderValue) { oldValue, newValue in
            player.updateBPM(newValue)
        }
        .sheet(isPresented: $showRunSummary) {
            RunSummaryView(
                distance: locationManager.distance,
                duration: locationManager.duration,
                pace: locationManager.pace,
                locations: locationManager.locations
            )
        }
        HStack {
            Spacer()
            Button(action: {
                showMenu = true
            }) {
                Image(systemName: "questionmark.circle")
                    .font(.title)
                    .foregroundColor(.green)
            }
            .padding()
            .sheet(isPresented: $showMenu) {
                MenuView()
                    .presentationDetents([.fraction(0.25)])
            }
            
            // 测试按钮 - 仅用于调试
            Button(action: {
                let runRecords = DataManager.shared.fetchRunRecords(context: modelContext)
                print("Total run records: \(runRecords.count)")
                
                for runRecord in runRecords {
                    let gpsLocations = DataManager.shared.fetchGPSLocations(for: runRecord.id, context: modelContext)
                    print("Run record \(runRecord.id) has \(gpsLocations.count) GPS locations")
                }
            }) {
                Image(systemName: "testtube.2")
                    .font(.title)
                    .foregroundColor(.blue)
            }
            .padding()
        }
        .padding()
    }
    
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        let seconds = Int(timeInterval) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

#Preview {
    ContentView()
}
