//
//  RunRecord.swift
//  cadence
//
//  Created by System on 7/23/25.
//

import Foundation
import SwiftData

@Model
final class RunRecord {
    @Attribute(.unique) var id: UUID
    var startTime: Date
    var endTime: Date?
    var distance: Double
    var duration: TimeInterval
    var pace: Double
    
    init(startTime: Date, distance: Double, duration: TimeInterval, pace: Double) {
        self.id = UUID()
        self.startTime = startTime
        self.distance = distance
        self.duration = duration
        self.pace = pace
    }
}
