//
//  GPSLocation.swift
//  cadence
//
//  Created by System on 7/23/25.
//

import Foundation
import CoreLocation
import SwiftData

@Model
final class GPSLocation {
    @Attribute(.unique) var id: UUID
    var latitude: Double
    var longitude: Double
    var timestamp: Date
    var coordinateType: CoordinateType
    var runRecordId: UUID
    
    init(latitude: Double, longitude: Double, timestamp: Date, coordinateType: CoordinateType, runRecordId: UUID) {
        self.id = UUID()
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = timestamp
        self.coordinateType = coordinateType
        self.runRecordId = runRecordId
    }
}

enum CoordinateType: String, Codable {
    case wgs84 = "WGS84"
    case gcj02 = "GCJ02"
    case bd09 = "BD09"
}
