//
//  LocationManager.swift
//  cadence
//
//  Created by System on 7/22/25.
//

import Foundation
import CoreLocation
import MapKit
import SwiftData

@MainActor
class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    @Published var locations: [CLLocation] = []
    private var modelContext: ModelContext?
    private var currentRunRecord: RunRecord?
    @Published var currentLocation: CLLocation?
    @Published var isRecording = false
    @Published var distance: Double = 0
    @Published var pace: Double = 0
    @Published var duration: TimeInterval = 0
    @Published var needsLocationPermission = false
    
    private let locationManager = CLLocationManager()
    private var startTime: Date?
    private var lastLocation: CLLocation?
    private var durationTimer: Timer?
    
    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.requestWhenInUseAuthorization()
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.distanceFilter = 5 // 每5米更新一次
        
        // 检查初始权限状态
        checkLocationPermission()
    }
    
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
    }
    
    func startRecording() {
        let authStatus = locationManager.authorizationStatus
        
        if authStatus == .notDetermined {
            locationManager.requestWhenInUseAuthorization()
            return
        }
        
        guard authStatus == .authorizedWhenInUse || authStatus == .authorizedAlways else {
            print("Location permission not granted, status: \(authStatus)")
            return
        }
        
        print("Starting GPS recording...")
        isRecording = true
        startTime = Date()
        locations.removeAll()
        distance = 0
        duration = 0
        lastLocation = nil
        
        // 创建新的运动记录
        currentRunRecord = RunRecord(startTime: startTime!, distance: 0, duration: 0, pace: 0)
        
        locationManager.startUpdatingLocation()
        
        // 启动计时器更新持续时间
        durationTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            if let startTime = self?.startTime {
                self?.duration = Date().timeIntervalSince(startTime)
            }
        }
    }
    
    func stopRecording() {
        isRecording = false
        locationManager.stopUpdatingLocation()
        durationTimer?.invalidate()
        durationTimer = nil
        
        if let start = startTime {
            duration = Date().timeIntervalSince(start)
        }
        
        // 保存运动记录
        if let runRecord = currentRunRecord, let modelContext = modelContext {
            runRecord.endTime = Date()
            runRecord.distance = distance
            runRecord.duration = duration
            runRecord.pace = pace
            
            modelContext.insert(runRecord)
            
            // 保存上下文
            do {
                try modelContext.save()
                print("Run record saved with ID: \(runRecord.id)")
            } catch {
                print("Failed to save run record: \(error)")
            }
        }
    }
    
    // MARK: - CLLocationManagerDelegate
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        // 过滤掉精度太低的位置
        guard location.horizontalAccuracy < 20 else { 
            print("Location accuracy too low: \(location.horizontalAccuracy)")
            return 
        }
        
        print("New location: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        
        currentLocation = location
        self.locations.append(location)
        
        print("Total locations: \(self.locations.count)")
        
        // 保存GPS位置数据
        if let runRecord = currentRunRecord, let modelContext = modelContext {
            // 应用坐标转换
            let transformedLocation = CoordinateTransformer.transformLocation(location)
            
            // 确定坐标类型
            let coordinateType: CoordinateType = CoordinateTransformer.isLocationInChina(
                latitude: location.coordinate.latitude,
                longitude: location.coordinate.longitude
            ) ? .gcj02 : .wgs84
            
            // 创建GPS位置记录
            let gpsLocation = GPSLocation(
                latitude: transformedLocation.coordinate.latitude,
                longitude: transformedLocation.coordinate.longitude,
                timestamp: location.timestamp,
                coordinateType: coordinateType,
                runRecordId: runRecord.id
            )
            
            modelContext.insert(gpsLocation)
            
            // 批量保存以提高性能
            if self.locations.count % 10 == 0 {
                do {
                    try modelContext.save()
                    print("GPS locations saved, count: \(self.locations.count)")
                } catch {
                    print("Failed to save GPS locations: \(error)")
                }
            }
        }
        
        // 计算距离
        if let last = lastLocation {
            let deltaDistance = location.distance(from: last)
            distance += deltaDistance
            print("Distance added: \(deltaDistance), Total distance: \(distance)")
            
            // 计算配速 (分钟/公里)
            if let start = startTime {
                let elapsedTime = Date().timeIntervalSince(start)
                duration = elapsedTime
                if distance > 0 {
                    pace = (elapsedTime / 60.0) / (distance / 1000.0)
                }
            }
        }
        
        lastLocation = location
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("Location manager failed with error: \(error)")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        checkLocationPermission()
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            print("Location permission granted")
        case .denied, .restricted:
            print("Location permission denied")
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        @unknown default:
            break
        }
    }
    
    private func checkLocationPermission() {
        let status = locationManager.authorizationStatus
        needsLocationPermission = status == .denied || status == .restricted
    }
    
    // 获取地图区域
    func getMapRegion() -> MKCoordinateRegion? {
        guard !locations.isEmpty else { return nil }
        
        let latitudes = locations.map { $0.coordinate.latitude }
        let longitudes = locations.map { $0.coordinate.longitude }
        
        let minLat = latitudes.min() ?? 0
        let maxLat = latitudes.max() ?? 0
        let minLon = longitudes.min() ?? 0
        let maxLon = longitudes.max() ?? 0
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: max(maxLat - minLat, 0.01) * 1.2,
            longitudeDelta: max(maxLon - minLon, 0.01) * 1.2
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
    
    // MARK: - Test Data (仅用于调试)
    func addTestLocations() {
        // 创建一些测试位置数据（北京地区）
        let testCoordinates = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084),
            CLLocationCoordinate2D(latitude: 39.9062, longitude: 116.4094),
            CLLocationCoordinate2D(latitude: 39.9072, longitude: 116.4104),
            CLLocationCoordinate2D(latitude: 39.9082, longitude: 116.4114),
        ]
        
        locations = testCoordinates.map { coordinate in
            CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        }
        
        distance = 500 // 500米测试距离
        duration = 300 // 5分钟测试时间
        pace = 6.0 // 6分钟每公里的测试配速
        
        print("Added test locations: \(locations.count)")
    }
}
