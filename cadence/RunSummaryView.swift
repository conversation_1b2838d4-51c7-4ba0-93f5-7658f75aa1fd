//
//  RunSummaryView.swift
//  cadence
//
//  Created by System on 7/22/25.
//

import SwiftUI
import MapKit
import CoreLocation

struct RunSummaryView: View {
    let distance: Double
    let duration: TimeInterval
    let pace: Double
    let locations: [CLLocation]
    
    @Environment(\.dismiss) private var dismiss
    @State private var shareImage: UIImage?
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("跑步完成！")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                // 地图缩略图
                if locations.isEmpty {
                    VStack {
                        Image(systemName: "map")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                        Text("没有GPS数据")
                            .foregroundColor(.secondary)
                    }
                    .frame(height: 200)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(15)
                    .padding(.horizontal)
                } else {
                    MapSummaryView(locations: locations)
                        .frame(height: 200)
                        .cornerRadius(15)
                        .padding(.horizontal)
                }
                
                // 统计数据
                VStack(spacing: 15) {
                    StatRow(title: "距离", value: String(format: "%.2f km", distance / 1000), icon: "location")
                    StatRow(title: "时间", value: formatTime(duration), icon: "timer")
                    StatRow(title: "配速", value: String(format: "%.1f min/km", pace), icon: "speedometer")
                    StatRow(title: "平均速度", value: String(format: "%.1f km/h", (distance / 1000) / (duration / 3600)), icon: "gauge")
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(15)
                .padding(.horizontal)
                
                Spacer()
                
                // 分享按钮
                Button(action: generateAndShareImage) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("分享跑步记录")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.green)
                    .cornerRadius(15)
                }
                .padding(.horizontal)
            }
            .navigationTitle("跑步总结")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            if let image = shareImage {
                ActivityView(activityItems: [image])
            }
        }
    }
    
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        let seconds = Int(timeInterval) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
    
    private func generateAndShareImage() {
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 400, height: 600))
        
        let image = renderer.image { context in
            // 背景
            UIColor.systemBackground.setFill()
            context.fill(CGRect(origin: .zero, size: CGSize(width: 400, height: 600)))
            
            // 标题
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.boldSystemFont(ofSize: 24),
                .foregroundColor: UIColor.systemGreen
            ]
            let title = "我的跑步记录"
            let titleSize = title.size(withAttributes: titleAttributes)
            title.draw(at: CGPoint(x: (400 - titleSize.width) / 2, y: 30), withAttributes: titleAttributes)
            
            // 创建地图图片
            if let mapImage = createMapImage() {
                mapImage.draw(in: CGRect(x: 20, y: 80, width: 360, height: 200))
            }
            
            // 统计数据
            let dataAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18),
                .foregroundColor: UIColor.label
            ]
            
            let stats = [
                "距离: \(String(format: "%.2f km", distance / 1000))",
                "时间: \(formatTime(duration))",
                "配速: \(String(format: "%.1f min/km", pace))",
                "平均速度: \(String(format: "%.1f km/h", (distance / 1000) / (duration / 3600)))"
            ]
            
            for (index, stat) in stats.enumerated() {
                let y = 320 + CGFloat(index * 40)
                stat.draw(at: CGPoint(x: 40, y: y), withAttributes: dataAttributes)
            }
            
            // 底部应用名称
            let appAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.systemGray
            ]
            let appName = "Cadence - 跑步节拍器"
            let appSize = appName.size(withAttributes: appAttributes)
            appName.draw(at: CGPoint(x: (400 - appSize.width) / 2, y: 550), withAttributes: appAttributes)
        }
        
        shareImage = image
        showingShareSheet = true
    }
    
    private func createMapImage() -> UIImage? {
        guard !locations.isEmpty else { return nil }
        
        let mapView = MKMapView(frame: CGRect(x: 0, y: 0, width: 360, height: 200))
        
        // 设置地图区域
        if let region = getMapRegion(for: locations) {
            mapView.setRegion(region, animated: false)
        }
        
        // 添加路径
        let coordinates = locations.map { $0.coordinate }
        let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
        mapView.addOverlay(polyline)
        
        // 渲染地图为图片
        let renderer = UIGraphicsImageRenderer(size: mapView.frame.size)
        return renderer.image { _ in
            mapView.drawHierarchy(in: mapView.frame, afterScreenUpdates: true)
        }
    }
    
    private func getMapRegion(for locations: [CLLocation]) -> MKCoordinateRegion? {
        guard !locations.isEmpty else { return nil }
        
        let latitudes = locations.map { $0.coordinate.latitude }
        let longitudes = locations.map { $0.coordinate.longitude }
        
        let minLat = latitudes.min() ?? 0
        let maxLat = latitudes.max() ?? 0
        let minLon = longitudes.min() ?? 0
        let maxLon = longitudes.max() ?? 0
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: max(maxLat - minLat, 0.01) * 1.2,
            longitudeDelta: max(maxLon - minLon, 0.01) * 1.2
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
}

struct MapSummaryView: UIViewRepresentable {
    let locations: [CLLocation]
    
    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.isUserInteractionEnabled = false
        return mapView
    }
    
    func updateUIView(_ mapView: MKMapView, context: Context) {
        guard !locations.isEmpty else { 
            print("MapSummaryView: No locations to display")
            return 
        }
        
        print("MapSummaryView: Displaying \(locations.count) locations")
        
        // 移除旧的路径
        mapView.removeOverlays(mapView.overlays)
        
        // 添加路径
        let coordinates = locations.map { $0.coordinate }
        let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
        mapView.addOverlay(polyline)
        
        // 设置地图区域
        if let region = getMapRegion(for: locations) {
            print("MapSummaryView: Setting region - center: \(region.center), span: \(region.span)")
            mapView.setRegion(region, animated: false)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, MKMapViewDelegate {
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            if let polyline = overlay as? MKPolyline {
                let renderer = MKPolylineRenderer(polyline: polyline)
                renderer.strokeColor = .systemGreen
                renderer.lineWidth = 3
                return renderer
            }
            return MKOverlayRenderer()
        }
    }
    
    private func getMapRegion(for locations: [CLLocation]) -> MKCoordinateRegion? {
        guard !locations.isEmpty else { return nil }
        
        let latitudes = locations.map { $0.coordinate.latitude }
        let longitudes = locations.map { $0.coordinate.longitude }
        
        let minLat = latitudes.min() ?? 0
        let maxLat = latitudes.max() ?? 0
        let minLon = longitudes.min() ?? 0
        let maxLon = longitudes.max() ?? 0
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: max(maxLat - minLat, 0.01) * 1.2,
            longitudeDelta: max(maxLon - minLon, 0.01) * 1.2
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
}

struct StatRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.green)
                .frame(width: 30)
            
            Text(title)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontWeight(.semibold)
        }
    }
}

struct ActivityView: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    RunSummaryView(
        distance: 5000,
        duration: 1800,
        pace: 6.0,
        locations: []
    )
}
