//
//  DataManager.swift
//  cadence
//
//  Created by System on 7/23/25.
//

import Foundation
import SwiftData

@MainActor
class DataManager {
    static let shared = DataManager()
    
    private init() {}
    
    func fetchRunRecords(context: ModelContext) -> [RunRecord] {
        do {
            let fetchDescriptor = FetchDescriptor<RunRecord>()
            return try context.fetch(fetchDescriptor)
        } catch {
            print("Failed to fetch run records: \(error)")
            return []
        }
    }
    
    func fetchGPSLocations(for runRecordId: UUID, context: ModelContext) -> [GPSLocation] {
        do {
            let predicate = #Predicate<GPSLocation> { $0.runRecordId == runRecordId }
            let fetchDescriptor = FetchDescriptor<GPSLocation>(predicate: predicate)
            return try context.fetch(fetchDescriptor)
        } catch {
            print("Failed to fetch GPS locations: \(error)")
            return []
        }
    }
    
    func deleteAllData(context: ModelContext) {
        do {
            // 删除所有GPS位置
            let gpsFetchDescriptor = FetchDescriptor<GPSLocation>()
            try context.delete(model: GPSLocation.self, where: gpsFetchDescriptor.predicate)
            
            // 删除所有运动记录
            let runFetchDescriptor = FetchDescriptor<RunRecord>()
            try context.delete(model: RunRecord.self, where: runFetchDescriptor.predicate)
            
            try context.save()
            print("All data deleted")
        } catch {
            print("Failed to delete data: \(error)")
        }
    }
}
