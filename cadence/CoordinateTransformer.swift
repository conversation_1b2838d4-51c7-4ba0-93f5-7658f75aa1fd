//
//  CoordinateTransformer.swift
//  cadence
//
//  Created by System on 7/23/25.
//

import Foundation
import CoreLocation

class CoordinateTransformer {
    // 地球半径（米）
    private static let EARTH_RADIUS: Double = 6378137.0
    
    // 中国区域的近似边界
    private static let CHINA_MIN_LAT: Double = 18.0
    private static let CHINA_MAX_LAT: Double = 54.0
    private static let CHINA_MIN_LON: Double = 73.0
    private static let CHINA_MAX_LON: Double = 136.0
    
    /**
     * 判断坐标是否在中国大陆范围内
     */
    static func isLocationInChina(latitude: Double, longitude: Double) -> Bool {
        return latitude >= CHINA_MIN_LAT && latitude <= CHINA_MAX_LAT &&
               longitude >= CHINA_MIN_LON && longitude <= CHINA_MAX_LON
    }
    
    /**
     * WGS-84坐标转换为GCJ-02坐标（火星坐标）
     * 仅对中国大陆范围内的坐标进行转换
     */
    static func transformWGS84ToGCJ02(latitude: Double, longitude: Double) -> (lat: Double, lon: Double) {
        // 如果不在中国范围内，不进行转换
        if !isLocationInChina(latitude: latitude, longitude: longitude) {
            return (latitude, longitude)
        }
        
        let lat = latitude
        let lon = longitude
        
        let dLat = transformLat(x: lon - 105.0, y: lat - 35.0)
        let dLon = transformLon(x: lon - 105.0, y: lat - 35.0)
        
        let radLat = lat / 180.0 * Double.pi
        var magic = sin(radLat)
        magic = 1 - 0.006693421622965943 * magic * magic
        let sqrtMagic = sqrt(magic)
        
        let adjustedDLat = (dLat * 180.0) / ((EARTH_RADIUS * (1 - 0.006693421622965943)) / sqrtMagic * Double.pi)
        let adjustedDLon = (dLon * 180.0) / (EARTH_RADIUS / sqrtMagic * cos(radLat) * Double.pi)
        
        let mgLat = lat + adjustedDLat
        let mgLon = lon + adjustedDLon
        
        return (mgLat, mgLon)
    }
    
    /**
     * 辅助函数：计算纬度偏移
     */
    private static func transformLat(x: Double, y: Double) -> Double {
        var ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(abs(x))
        ret += (20.0 * sin(6.0 * x * Double.pi) + 20.0 * sin(2.0 * x * Double.pi)) * 2.0 / 3.0
        ret += (20.0 * sin(y * Double.pi) + 40.0 * sin(y / 3.0 * Double.pi)) * 2.0 / 3.0
        ret += (160.0 * sin(y / 12.0 * Double.pi) + 320 * sin(y * Double.pi / 30.0)) * 2.0 / 3.0
        return ret
    }
    
    /**
     * 辅助函数：计算经度偏移
     */
    private static func transformLon(x: Double, y: Double) -> Double {
        var ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(abs(x))
        ret += (20.0 * sin(6.0 * x * Double.pi) + 20.0 * sin(2.0 * x * Double.pi)) * 2.0 / 3.0
        ret += (20.0 * sin(x * Double.pi) + 40.0 * sin(x / 3.0 * Double.pi)) * 2.0 / 3.0
        ret += (150.0 * sin(x / 12.0 * Double.pi) + 300.0 * sin(x / 30.0 * Double.pi)) * 2.0 / 3.0
        return ret
    }
    
    /**
     * 转换CLLocation坐标
     */
    static func transformLocation(_ location: CLLocation) -> CLLocation {
        let (lat, lon) = transformWGS84ToGCJ02(latitude: location.coordinate.latitude, longitude: location.coordinate.longitude)
        return CLLocation(latitude: lat, longitude: lon)
    }
}
