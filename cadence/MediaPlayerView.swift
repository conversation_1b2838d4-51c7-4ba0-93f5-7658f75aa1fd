//
//  MediaPlayerView.swift
//  cadence
//
//  Created by Assistant on 23/7/25.
//

import SwiftUI
import MediaPlayer
import AVFoundation

class MediaPlayerManager: ObservableObject {
    @Published var isPlayingMusic = false
    @Published var currentTrackTitle = "No track playing"
    @Published var currentArtist = "Unknown Artist"
    @Published var currentAlbum = "Unknown Album"
    @Published var selectedTab = 0 // 0 for Apple Music, 1 for Podcasts
    @Published var musicPlaylists: [MPMediaPlaylist] = []
    @Published var podcastShows: [MPMediaItemCollection] = []
    @Published var selectedPlaylist: MPMediaPlaylist?
    @Published var selectedPodcastShow: MPMediaItemCollection?
    
    private var remoteCommandCenter: MPRemoteCommandCenter
    private let musicPlayer = MPMusicPlayerController.systemMusicPlayer
    
    init() {
        remoteCommandCenter = MPRemoteCommandCenter.shared()
        setupRemoteCommands()
        loadMediaLibrary()
    }
    
    private func setupRemoteCommands() {
        remoteCommandCenter.playCommand.addTarget { [weak self] _ in
            self?.playMusic()
            return .success
        }
        
        remoteCommandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pauseMusic()
            return .success
        }
        
        remoteCommandCenter.nextTrackCommand.addTarget { [weak self] _ in
            self?.nextTrack()
            return .success
        }
        
        remoteCommandCenter.previousTrackCommand.addTarget { [weak self] _ in
            self?.previousTrack()
            return .success
        }
    }
    
    func loadMediaLibrary() {
        // 加载 Apple Music 播放列表
        let playlistsQuery = MPMediaQuery.playlists()
        musicPlaylists = playlistsQuery.collections?.compactMap { $0 as? MPMediaPlaylist } ?? []
        
        // 加载播客节目
        let podcastQuery = MPMediaQuery.podcasts()
        let podcastItems = podcastQuery.items ?? []
        
        // 按节目分组播客
        var showsDict: [String: [MPMediaItem]] = [:]
        for item in podcastItems {
            let showTitle = item.podcastTitle ?? "Unknown Show"
            if showsDict[showTitle] == nil {
                showsDict[showTitle] = []
            }
            showsDict[showTitle]?.append(item)
        }
        
        // 创建播客节目集合
        podcastShows = showsDict.compactMap { (showTitle, items) in
            return MPMediaItemCollection(items: items)
        }
    }
    
    func playMusic() {
        musicPlayer.play()
        isPlayingMusic = true
        updateNowPlayingInfo()
    }
    
    func pauseMusic() {
        musicPlayer.pause()
        isPlayingMusic = false
        updateNowPlayingInfo()
    }
    
    func nextTrack() {
        musicPlayer.skipToNextItem()
        updateNowPlayingInfo()
    }
    
    func previousTrack() {
        musicPlayer.skipToPreviousItem()
        updateNowPlayingInfo()
    }
    
    func playPlaylist(_ playlist: MPMediaPlaylist) {
        selectedPlaylist = playlist
        musicPlayer.setQueue(with: playlist)
        playMusic()
    }
    
    func playPodcastShow(_ show: MPMediaItemCollection) {
        selectedPodcastShow = show
        musicPlayer.setQueue(with: show)
        playMusic()
    }
    
    private func updateNowPlayingInfo() {
        var nowPlayingInfo = [String: Any]()
        
        if let nowPlayingItem = musicPlayer.nowPlayingItem {
            let title = nowPlayingItem.title ?? "Unknown Title"
            let artist = nowPlayingItem.artist ?? nowPlayingItem.podcastTitle ?? "Unknown Artist"
            let album = nowPlayingItem.albumTitle ?? "Unknown Album"
            
            nowPlayingInfo[MPMediaItemPropertyTitle] = title
            nowPlayingInfo[MPMediaItemPropertyArtist] = artist
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = album
            
            currentTrackTitle = title
            currentArtist = artist
            currentAlbum = album
        } else {
            nowPlayingInfo[MPMediaItemPropertyTitle] = "Cadence App"
            nowPlayingInfo[MPMediaItemPropertyArtist] = "Running Playlist"
            currentTrackTitle = "No track playing"
            currentArtist = "Unknown Artist"
            currentAlbum = "Unknown Album"
        }
        
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlayingMusic ? 1.0 : 0.0
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = musicPlayer.currentPlaybackTime
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}

struct MediaPlayerView: View {
    @StateObject private var mediaManager = MediaPlayerManager()
    @State private var isExpanded = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部媒体控制区域
            VStack {
                // Tab 选择器
                HStack {
                    Button(action: {
                        mediaManager.selectedTab = 0
                    }) {
                        Text("Apple Music")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(mediaManager.selectedTab == 0 ? Color.blue : Color.gray.opacity(0.2))
                            .foregroundColor(mediaManager.selectedTab == 0 ? .white : .primary)
                            .cornerRadius(20)
                    }
                    
                    Button(action: {
                        mediaManager.selectedTab = 1
                    }) {
                        Text("Podcasts")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(mediaManager.selectedTab == 1 ? Color.blue : Color.gray.opacity(0.2))
                            .foregroundColor(mediaManager.selectedTab == 1 ? .white : .primary)
                            .cornerRadius(20)
                    }
                    
                    Spacer()
                    
                    // 展开/收起按钮
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    }) {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)
                    }
                }
                .padding(.horizontal)
                .padding(.top, 10)
                
                // 当前播放信息
                if !mediaManager.currentTrackTitle.isEmpty && mediaManager.currentTrackTitle != "No track playing" {
                    HStack {
                        VStack(alignment: .leading) {
                            Text(mediaManager.currentTrackTitle)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .lineLimit(1)
                            Text(mediaManager.currentArtist)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                        
                        Spacer()
                        
                        // 播放控制按钮
                        HStack(spacing: 20) {
                            Button(action: {
                                mediaManager.previousTrack()
                            }) {
                                Image(systemName: "backward.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(.blue)
                            }
                            
                            Button(action: {
                                if mediaManager.isPlayingMusic {
                                    mediaManager.pauseMusic()
                                } else {
                                    mediaManager.playMusic()
                                }
                            }) {
                                Image(systemName: mediaManager.isPlayingMusic ? "pause.fill" : "play.fill")
                                    .font(.system(size: 18))
                                    .foregroundColor(.blue)
                            }
                            
                            Button(action: {
                                mediaManager.nextTrack()
                            }) {
                                Image(systemName: "forward.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 10)
                }
            }
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // 展开的播放列表区域
            if isExpanded {
                VStack {
                    if mediaManager.selectedTab == 0 {
                        // Apple Music 播放列表
                        AppleMusicPlaylistView(mediaManager: mediaManager)
                    } else {
                        // Podcast 列表
                        PodcastShowListView(mediaManager: mediaManager)
                    }
                }
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .gray.opacity(0.2), radius: 4, x: 0, y: 2)
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .onAppear {
            // 请求媒体库访问权限
            MPMediaLibrary.requestAuthorization { status in
                if status == .authorized {
                    DispatchQueue.main.async {
                        mediaManager.loadMediaLibrary()
                    }
                }
            }
        }
    }
}

struct AppleMusicPlaylistView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("播放列表")
                .font(.headline)
                .padding(.horizontal)
                .padding(.top)
            
            if mediaManager.musicPlaylists.isEmpty {
                VStack {
                    Image(systemName: "music.note.list")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                    Text("没有找到播放列表")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Text("请确保已在音乐应用中创建播放列表")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(height: 120)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(mediaManager.musicPlaylists.prefix(5), id: \.persistentID) { playlist in
                        Button(action: {
                            mediaManager.playPlaylist(playlist)
                        }) {
                            HStack {
                                Image(systemName: "music.note.list")
                                    .font(.system(size: 20))
                                    .foregroundColor(.blue)
                                    .frame(width: 30)
                                
                                VStack(alignment: .leading) {
                                    Text(playlist.name ?? "Unknown Playlist")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.primary)
                                        .lineLimit(1)
                                    
                                    Text("\(playlist.count) 首歌")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                if playlist == mediaManager.selectedPlaylist {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.system(size: 16))
                                        .foregroundColor(.blue)
                                }
                            }
                            .padding(.horizontal)
                            .padding(.vertical, 8)
                            .background(playlist == mediaManager.selectedPlaylist ? Color.blue.opacity(0.1) : Color.clear)
                            .cornerRadius(8)
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
        }
        .frame(maxHeight: 200)
    }
}

struct PodcastShowListView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("播客节目")
                .font(.headline)
                .padding(.horizontal)
                .padding(.top)
            
            if mediaManager.podcastShows.isEmpty {
                VStack {
                    Image(systemName: "mic.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                    Text("没有找到播客节目")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Text("请确保已在播客应用中下载播客")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(height: 120)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(mediaManager.podcastShows.prefix(5), id: \.persistentID) { show in
                        Button(action: {
                            mediaManager.playPodcastShow(show)
                        }) {
                            HStack {
                                Image(systemName: "mic.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.orange)
                                    .frame(width: 30)
                                
                                VStack(alignment: .leading) {
                                    Text(show.items.first?.podcastTitle ?? "Unknown Podcast")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.primary)
                                        .lineLimit(1)
                                    
                                    Text("\(show.count) 集")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                if show == mediaManager.selectedPodcastShow {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.system(size: 16))
                                        .foregroundColor(.orange)
                                }
                            }
                            .padding(.horizontal)
                            .padding(.vertical, 8)
                            .background(show == mediaManager.selectedPodcastShow ? Color.orange.opacity(0.1) : Color.clear)
                            .cornerRadius(8)
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
        }
        .frame(maxHeight: 200)
    }
}

#Preview {
    MediaPlayerView()
        .padding()
}
