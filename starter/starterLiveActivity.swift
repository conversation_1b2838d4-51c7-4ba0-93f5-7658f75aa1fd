//
//  starterLiveActivity.swift
//  starter
//
//  Created by <PERSON> on 1/23/25.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct starterAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        var emoji: String
    }

    // Fixed non-changing properties about your activity go here!
    var name: String
}

struct starterLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: starterAttributes.self) { context in
            // Lock screen/banner UI goes here
            VStack {
                Text("Hello \(context.state.emoji)")
            }
            .activityBackgroundTint(Color.cyan)
            .activitySystemActionForegroundColor(Color.black)

        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text("Bottom \(context.state.emoji)")
                    // more content
                }
            } compactLeading: {
                Text("L")
            } compactTrailing: {
                Text("T \(context.state.emoji)")
            } minimal: {
                Text(context.state.emoji)
            }
            .widgetURL(URL(string: "http://www.apple.com"))
            .keylineTint(Color.red)
        }
    }
}

extension starterAttributes {
    fileprivate static var preview: starterAttributes {
        starterAttributes(name: "World")
    }
}

extension starterAttributes.ContentState {
    fileprivate static var smiley: starterAttributes.ContentState {
        starterAttributes.ContentState(emoji: "😀")
     }
     
     fileprivate static var starEyes: starterAttributes.ContentState {
         starterAttributes.ContentState(emoji: "🤩")
     }
}

#Preview("Notification", as: .content, using: starterAttributes.preview) {
   starterLiveActivity()
} contentStates: {
    starterAttributes.ContentState.smiley
    starterAttributes.ContentState.starEyes
}
