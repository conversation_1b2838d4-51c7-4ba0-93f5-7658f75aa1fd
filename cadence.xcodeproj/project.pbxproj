// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		645D18872E2FCD9600B8227B /* MapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 645D18862E2FCD9600B8227B /* MapKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		646632302D0172E4009B2A69 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 646632172D0172E3009B2A69 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6466321E2D0172E3009B2A69;
			remoteInfo = cadence;
		};
		6466323A2D0172E4009B2A69 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 646632172D0172E3009B2A69 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6466321E2D0172E3009B2A69;
			remoteInfo = cadence;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		64F366CC2D42365C00FBD659 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		645D18862E2FCD9600B8227B /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = System/Library/Frameworks/MapKit.framework; sourceTree = SDKROOT; };
		6466321F2D0172E3009B2A69 /* cadence.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = cadence.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6466322F2D0172E4009B2A69 /* cadenceTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = cadenceTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		646632392D0172E4009B2A69 /* cadenceUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = cadenceUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		64F366B32D42365B00FBD659 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		64F366B52D42365B00FBD659 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		6466324D2D017A0C009B2A69 /* Exceptions for "cadence" folder in "cadence" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 6466321E2D0172E3009B2A69 /* cadence */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		646632212D0172E3009B2A69 /* cadence */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				6466324D2D017A0C009B2A69 /* Exceptions for "cadence" folder in "cadence" target */,
			);
			path = cadence;
			sourceTree = "<group>";
		};
		646632322D0172E4009B2A69 /* cadenceTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = cadenceTests;
			sourceTree = "<group>";
		};
		6466323C2D0172E4009B2A69 /* cadenceUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = cadenceUITests;
			sourceTree = "<group>";
		};
		64F366B72D42365B00FBD659 /* starter */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = starter;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		6466321C2D0172E3009B2A69 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				645D18872E2FCD9600B8227B /* MapKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6466322C2D0172E4009B2A69 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		646632362D0172E4009B2A69 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		646632162D0172E3009B2A69 = {
			isa = PBXGroup;
			children = (
				646632212D0172E3009B2A69 /* cadence */,
				646632322D0172E4009B2A69 /* cadenceTests */,
				6466323C2D0172E4009B2A69 /* cadenceUITests */,
				64F366B72D42365B00FBD659 /* starter */,
				64F366B22D42365B00FBD659 /* Frameworks */,
				646632202D0172E3009B2A69 /* Products */,
			);
			sourceTree = "<group>";
		};
		646632202D0172E3009B2A69 /* Products */ = {
			isa = PBXGroup;
			children = (
				6466321F2D0172E3009B2A69 /* cadence.app */,
				6466322F2D0172E4009B2A69 /* cadenceTests.xctest */,
				646632392D0172E4009B2A69 /* cadenceUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		64F366B22D42365B00FBD659 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				645D18862E2FCD9600B8227B /* MapKit.framework */,
				64F366B32D42365B00FBD659 /* WidgetKit.framework */,
				64F366B52D42365B00FBD659 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6466321E2D0172E3009B2A69 /* cadence */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 646632432D0172E4009B2A69 /* Build configuration list for PBXNativeTarget "cadence" */;
			buildPhases = (
				6466321B2D0172E3009B2A69 /* Sources */,
				6466321C2D0172E3009B2A69 /* Frameworks */,
				6466321D2D0172E3009B2A69 /* Resources */,
				64F366CC2D42365C00FBD659 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				646632212D0172E3009B2A69 /* cadence */,
			);
			name = cadence;
			packageProductDependencies = (
			);
			productName = cadence;
			productReference = 6466321F2D0172E3009B2A69 /* cadence.app */;
			productType = "com.apple.product-type.application";
		};
		6466322E2D0172E4009B2A69 /* cadenceTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 646632462D0172E4009B2A69 /* Build configuration list for PBXNativeTarget "cadenceTests" */;
			buildPhases = (
				6466322B2D0172E4009B2A69 /* Sources */,
				6466322C2D0172E4009B2A69 /* Frameworks */,
				6466322D2D0172E4009B2A69 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				646632312D0172E4009B2A69 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				646632322D0172E4009B2A69 /* cadenceTests */,
			);
			name = cadenceTests;
			packageProductDependencies = (
			);
			productName = cadenceTests;
			productReference = 6466322F2D0172E4009B2A69 /* cadenceTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		646632382D0172E4009B2A69 /* cadenceUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 646632492D0172E4009B2A69 /* Build configuration list for PBXNativeTarget "cadenceUITests" */;
			buildPhases = (
				646632352D0172E4009B2A69 /* Sources */,
				646632362D0172E4009B2A69 /* Frameworks */,
				646632372D0172E4009B2A69 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6466323B2D0172E4009B2A69 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				6466323C2D0172E4009B2A69 /* cadenceUITests */,
			);
			name = cadenceUITests;
			packageProductDependencies = (
			);
			productName = cadenceUITests;
			productReference = 646632392D0172E4009B2A69 /* cadenceUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		646632172D0172E3009B2A69 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					6466321E2D0172E3009B2A69 = {
						CreatedOnToolsVersion = 16.1;
					};
					6466322E2D0172E4009B2A69 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 6466321E2D0172E3009B2A69;
					};
					646632382D0172E4009B2A69 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 6466321E2D0172E3009B2A69;
					};
				};
			};
			buildConfigurationList = 6466321A2D0172E3009B2A69 /* Build configuration list for PBXProject "cadence" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 646632162D0172E3009B2A69;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 646632202D0172E3009B2A69 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6466321E2D0172E3009B2A69 /* cadence */,
				6466322E2D0172E4009B2A69 /* cadenceTests */,
				646632382D0172E4009B2A69 /* cadenceUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6466321D2D0172E3009B2A69 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6466322D2D0172E4009B2A69 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		646632372D0172E4009B2A69 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6466321B2D0172E3009B2A69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6466322B2D0172E4009B2A69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		646632352D0172E4009B2A69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		646632312D0172E4009B2A69 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6466321E2D0172E3009B2A69 /* cadence */;
			targetProxy = 646632302D0172E4009B2A69 /* PBXContainerItemProxy */;
		};
		6466323B2D0172E4009B2A69 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6466321E2D0172E3009B2A69 /* cadence */;
			targetProxy = 6466323A2D0172E4009B2A69 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		646632412D0172E4009B2A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		646632422D0172E4009B2A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		646632442D0172E4009B2A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"cadence/Preview Content\"";
				DEVELOPMENT_TEAM = 7TML3NFBTM;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = cadence/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.socratop.cadence;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		646632452D0172E4009B2A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"cadence/Preview Content\"";
				DEVELOPMENT_TEAM = 7TML3NFBTM;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = cadence/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.socratop.cadence;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		646632472D0172E4009B2A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2STDV5X89Q;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.project5e.cadenceTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/cadence.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/cadence";
			};
			name = Debug;
		};
		646632482D0172E4009B2A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2STDV5X89Q;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.project5e.cadenceTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/cadence.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/cadence";
			};
			name = Release;
		};
		6466324A2D0172E4009B2A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2STDV5X89Q;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.project5e.cadenceUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = cadence;
			};
			name = Debug;
		};
		6466324B2D0172E4009B2A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2STDV5X89Q;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.project5e.cadenceUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = cadence;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6466321A2D0172E3009B2A69 /* Build configuration list for PBXProject "cadence" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				646632412D0172E4009B2A69 /* Debug */,
				646632422D0172E4009B2A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		646632432D0172E4009B2A69 /* Build configuration list for PBXNativeTarget "cadence" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				646632442D0172E4009B2A69 /* Debug */,
				646632452D0172E4009B2A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		646632462D0172E4009B2A69 /* Build configuration list for PBXNativeTarget "cadenceTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				646632472D0172E4009B2A69 /* Debug */,
				646632482D0172E4009B2A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		646632492D0172E4009B2A69 /* Build configuration list for PBXNativeTarget "cadenceUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6466324A2D0172E4009B2A69 /* Debug */,
				6466324B2D0172E4009B2A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 646632172D0172E3009B2A69 /* Project object */;
}
